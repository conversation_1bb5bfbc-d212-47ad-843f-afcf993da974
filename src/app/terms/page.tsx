import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Terms of Service - BenefitLens',
  description: 'Terms of service and usage conditions for BenefitLens',
}

export default function TermsPage() {
  return (
    <div className="py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white shadow-sm rounded-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Terms of Service</h1>

          <div className="prose prose-lg max-w-none">
            <div className="mb-6 p-4 bg-yellow-50 border-l-4 border-yellow-400 rounded">
              <p className="text-sm text-yellow-800 font-medium">
                <strong>Important Notice:</strong> This platform provides information about company benefits
                for informational purposes only. All information is provided without warranty. Please verify all
                benefit information directly with the respective companies.
              </p>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">1. <PERSON>ope</h2>
            <p className="mb-4 text-gray-600">
              These terms of service apply to the use of the BenefitLens platform ("Platform"),
              operated by <PERSON>úl <PERSON> <PERSON>. By using the platform, you agree to
              these terms.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">2. Service Description</h2>
            <p className="mb-4 text-gray-600">
              BenefitLens is an information platform that allows users to compare companies
              based on their employee benefits. The platform collects and displays
              information about company benefits from various sources.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">3. Data Accuracy Disclaimer</h2>
            <div className="mb-6 p-4 bg-red-50 border-l-4 border-red-400 rounded">
              <h3 className="text-lg font-semibold text-red-800 mb-2">Important Limitations:</h3>
              <ul className="list-disc list-inside text-red-700 space-y-2">
                <li>All benefit information is provided without warranty for accuracy, completeness, or timeliness</li>
                <li>Benefits may change at any time without being immediately reflected on the platform</li>
                <li>Users are required to verify all benefit information directly with the respective company</li>
                <li>BenefitLens assumes no liability for decisions made based on the information provided</li>
              </ul>
            </div>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">4. User Responsibilities</h2>
            <p className="mb-4 text-gray-600">As a user, you agree to:</p>
            <ul className="list-disc list-inside mb-4 text-gray-600 space-y-2">
              <li>Provide only truthful information</li>
              <li>Not submit false or misleading benefit information</li>
              <li>Respect the rights of third parties</li>
              <li>Not publish illegal content</li>
              <li>Verify benefit information before making important decisions</li>
            </ul>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">5. Data Collection and Processing</h2>
            <p className="mb-4 text-gray-600">
              By using the platform, you consent to the collection and processing of the following data:
            </p>
            <ul className="list-disc list-inside mb-4 text-gray-600 space-y-2">
              <li>Registration data (email, name)</li>
              <li>Usage behavior (page views, search history, interactions)</li>
              <li>Technical data (IP address, browser information, session IDs)</li>
              <li>Benefit information and reviews provided by you</li>
            </ul>
            <p className="mb-4 text-gray-600">
              Detailed information can be found in our
              <a href="/datenschutz" className="text-blue-600 hover:text-blue-800 ml-1">Privacy Policy</a>.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">6. Company Verification</h2>
            <p className="mb-4 text-gray-600">
              Automatic assignment to companies is based on email domains. This assignment:
            </p>
            <ul className="list-disc list-inside mb-4 text-gray-600 space-y-2">
              <li>Is automatic and may be incorrect</li>
              <li>Does not automatically authorize management of company data</li>
              <li>Can be reviewed and corrected at any time</li>
              <li>Is subject to additional verification procedures</li>
            </ul>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">7. Intellectual Property</h2>
            <p className="mb-4 text-gray-600">
              All platform content, including design, code, and database, is protected by copyright.
              Users may only use content for personal, non-commercial purposes.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">8. Limitation of Liability</h2>
            <p className="mb-4 text-gray-600">
              BenefitLens is not liable for:
            </p>
            <ul className="list-disc list-inside mb-4 text-gray-600 space-y-2">
              <li>Damages from incomplete or incorrect benefit information</li>
              <li>Decisions made based on platform information</li>
              <li>Losses due to platform unavailability</li>
              <li>Damages from actions of other users</li>
              <li>Indirect or consequential damages of any kind</li>
            </ul>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">9. Changes to Terms of Service</h2>
            <p className="mb-4 text-gray-600">
              We reserve the right to change these terms of service at any time. Changes will be
              published on the platform and take effect upon publication.
            </p>

            <h2 className="text-xl font-semibold text-gray-900 mb-4">10. Applicable Law</h2>
            <p className="mb-4 text-gray-600">
              German law applies. Place of jurisdiction is Sankt Augustin, Germany.
            </p>

            <div className="mt-8 p-4 bg-blue-50 border-l-4 border-blue-400 rounded">
              <p className="text-sm text-blue-800">
                <strong>Contact:</strong> For questions about these terms of service, contact:
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-800 ml-1">
                  <EMAIL>
                </a>
              </p>
              <p className="text-sm text-blue-800 mt-2">
                <strong>Last updated:</strong> {new Date().toLocaleDateString('en-US')}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
