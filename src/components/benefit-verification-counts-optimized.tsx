'use client'

interface BenefitVerificationCountsOptimizedProps {
  companyBenefitId: string
  className?: string
  verificationCounts?: {
    confirmed: number
    disputed: number
    total: number
  } | null
  isLoading?: boolean
}

export function BenefitVerificationCountsOptimized({ 
  companyBenefitId, 
  className = '',
  verificationCounts,
  isLoading = false
}: BenefitVerificationCountsOptimizedProps) {
  // Don't render anything if loading or no verifications
  if (isLoading || !verificationCounts || verificationCounts.total === 0) {
    return null
  }

  return (
    <div className={`text-xs text-gray-600 mt-2 ${className}`}>
      <div className="flex items-center space-x-1">
        <span className="text-green-600 font-medium">{verificationCounts.confirmed} confirmed</span>
        {verificationCounts.disputed > 0 && (
          <>
            <span>•</span>
            <span className="text-red-600 font-medium">{verificationCounts.disputed} disputed</span>
          </>
        )}
      </div>
      <div className="text-xs text-gray-600 mt-1">
        Benefits need 2+ confirmations and more confirmations than disputes to be verified
      </div>
    </div>
  )
}
