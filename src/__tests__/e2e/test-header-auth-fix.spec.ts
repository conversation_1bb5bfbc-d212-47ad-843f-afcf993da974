import { test, expect } from '@playwright/test'
import { signInUser } from './src/__tests__/e2e/auth-helpers'

test.describe('Header Authentication Update Fix', () => {
  test('header buttons should update immediately after magic link authentication', async ({ page }) => {
    console.log('🧪 Testing header authentication update fix...')
    
    // Start on the home page and verify unauthenticated state
    await page.goto('/')
    
    // Verify initial unauthenticated state
    await expect(page.locator('a[href="/sign-in"] button')).toBeVisible()
    await expect(page.locator('a[href="/sign-up"] button')).toBeVisible()
    
    console.log('✅ Initial unauthenticated state verified')
    
    // Sign in using the auth helper (which creates a real magic link token)
    await signInUser(page, 'user1@techcorp.e2e')
    
    console.log('✅ User signed in successfully')
    
    // Navigate back to home page to test header state
    await page.goto('/')
    
    // Wait a moment for the header to update
    await page.waitForTimeout(500)
    
    // Verify authenticated state - header should show "My Company" and user profile
    await expect(page.locator('a[href="/dashboard"] button')).toBeVisible({ timeout: 5000 })
    await expect(page.locator('button:has-text("John Doe")')).toBeVisible({ timeout: 5000 })
    
    // Verify unauthenticated buttons are gone
    await expect(page.locator('a[href="/sign-in"] button')).not.toBeVisible()
    await expect(page.locator('a[href="/sign-up"] button')).not.toBeVisible()
    
    console.log('✅ Header authentication update fix verified!')
  })
  
  test('header should update immediately during magic link verification process', async ({ page }) => {
    console.log('🧪 Testing header update during magic link verification...')
    
    // Start on the home page and verify unauthenticated state
    await page.goto('/')
    
    // Verify initial unauthenticated state
    await expect(page.locator('a[href="/sign-in"] button')).toBeVisible()
    await expect(page.locator('a[href="/sign-up"] button')).toBeVisible()
    
    console.log('✅ Initial unauthenticated state verified')
    
    // Create a magic link token manually
    const { Pool } = require('pg')
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL,
    })
    
    const email = 'user1@techcorp.e2e'
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2)
    const uniqueToken = `test-header-fix-${timestamp}-${random}`
    const futureExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
    
    await pool.query(`
      INSERT INTO magic_link_tokens (token, email, expires_at, used_at)
      VALUES ($1, $2, $3, null)
    `, [uniqueToken, email, futureExpiry])
    
    console.log(`🎫 Created token: ${uniqueToken}`)
    
    // Navigate to magic link verification page
    await page.goto(`/auth/magic-link#${uniqueToken}`)
    
    // Wait for the "Complete Sign In" button to appear
    const confirmButton = page.locator('button:has-text("Complete Sign In")')
    await expect(confirmButton).toBeVisible({ timeout: 10000 })
    
    console.log('✅ Magic link verification page loaded')
    
    // Before clicking, verify header still shows unauthenticated state
    await expect(page.locator('a[href="/sign-in"] button')).toBeVisible()
    await expect(page.locator('a[href="/sign-up"] button')).toBeVisible()
    
    console.log('✅ Header shows unauthenticated state before clicking Complete Sign In')
    
    // Click "Complete Sign In"
    await confirmButton.click()
    
    // Wait for authentication to complete
    await page.waitForTimeout(1000)
    
    // The header should now show authenticated state
    await expect(page.locator('a[href="/dashboard"] button')).toBeVisible({ timeout: 5000 })
    await expect(page.locator('button:has-text("John Doe")')).toBeVisible({ timeout: 5000 })
    
    // Verify unauthenticated buttons are gone
    await expect(page.locator('a[href="/sign-in"] button')).not.toBeVisible()
    await expect(page.locator('a[href="/sign-up"] button')).not.toBeVisible()
    
    console.log('✅ Header updated immediately after authentication!')
    
    // Clean up
    await pool.end()
  })
})
