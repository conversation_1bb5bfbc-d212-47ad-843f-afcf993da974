# Start everything (already running)
docker-compose up -d

# Development server (already running)
npm run dev
npm run build
npm outdated
npm run lint
npm install

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Reset database
docker-compose down -v && docker-compose up -d

# db commands
psql -d benefitlens -c "SELECT id, name, domain FROM companies WHERE name ILIKE '%mcdermott%' OR name ILIKE '%mwe%' OR domain = 'mwe.com';"

docker exec -it benefitlens-postgres psql -U benefitlens_user -d benefitlens -c "SELECT id, email, company_id FROM users WHERE email = '<EMAIL>';"

docker exec -it workwell-postgres psql -U workwell_user -d workwell -c "UPDATE users SET company_id = (SELECT id FROM companies WHERE domain = 'mwe.com') WHERE email = '<EMAIL>';"

docker exec benefitlens-postgres pg_dump -U benefitlens_user -d benefitlens | gzip > backup_$(date +%Y%m%d_%H%M%S).sql.gz

## migrate command
docker exec -i benefitlens-postgres psql -U benefitlens_user -d benefitlens -c "\d benefit_categories"
docker exec -i benefitlens-postgres psql -U benefitlens_user -d benefitlens -f /dev/stdin < database/migrations/020-remove-is-active-from-benefit-categories.sql


# curl commands
curl -X GET http://localhost:3000/api/admin/activities \
  -H "Cookie: $(curl -s -c - -X POST http://localhost:3000/api/auth/magic-link -H 'Content-Type: application/json' -d '{\"token\": \"d6b4e6183ae2f0c9740c07cc2ea9ae6991ba1d7c0625cf7cec7967eff2099ba9\"}' | grep -o 'session_token=[^;]*')"
curl https://benefitlens.de/api/companies?page=1&limit=30
curl -s -w "HTTP Status: %{http_code}\n" http://localhost:3001/api/saved-companies
curl -s "https://benefitlens.de/api/companies" | jq '.companies[] | {id, name}' | head -20

# Quick demo imports
npm run import:demo-companies:dry-run
npm run import:demo-benefits:dry-run

# Custom file imports
node scripts/import-data.js --type=companies --file=my-companies.csv
node scripts/import-data.js --type=companies --file=data/german-stock-indices-companies.json --overwrite

node scripts/import-data.js --type=benefits --file=my-benefits.json --category=health
node scripts/import-company-benefits.js --file=data/german-stock-indices-company-benefits.json --unverified --dry-run
node scripts/import-company-benefits.js --file=data/german-stock-indices-company-benefits.json --unverified
node scripts/import-company-benefits.js --file=data/german-stock-indices-company-benefits.json --unverified --overwrite

# Test App
npx tsc --noEmit 2>&1 | wc -l
npx tsc --noEmit 2>&1 | head -50
npm test
npm lint
npm run test:e2e:full -- --reporter=list
npm run test:e2e -- essential-tests.spec.ts
DISABLE_RATE_LIMITING=true npm run test:e2e -- user-journeys.spec.ts --grep "Error Handling Journey"
npx playwright test --config=playwright-no-rate-limit.config.ts --project="iPhone 14" src/__tests__/e2e/mobile-functionality-tests.spec.ts
npm test -- --run src/__tests__/email-template-readability.test.ts
npm test -- --run src/__tests__/user-authentication.test.ts
npm test -- --run src/__tests__/components/magic-link-verification.test.tsx
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Management Journey" --workers=3 --reporter=line
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Ranking Journey" --workers=3 --reporter=line
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Ranking Journey" --project="iPad Pro"
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Ranking Journey" --project="chromium"
npm run test:e2e -- src/__tests__/e2e/admin-journeys.spec.ts --grep "Admin Benefit Verification Journey" --project="chromium"
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Ranking Journey" --project="Mobile Chrome"
npx playwright test privacy-features --headed
docker-compose -f docker-compose.playwright.yml --profile testing run --rm playwright npx playwright test --config=playwright.doc
ker.config.ts --project=webkit --grep="Responsive Design"


# With custom field mappings
node scripts/import-data.js --type=companies --file=data.csv --mapping=custom.json


# Health checks - ALL PASSING ✅
npm run health:check     # ✅ Comprehensive health check
npm run health:simple    # ✅ Simple health check

# Environment validation - PASSING ✅  
npm run validate:env     # ✅ All required variables validated

# Application - RUNNING ✅
npm run dev             # ✅ Server running on http://localhost:3000
npm run build           # ✅ Build successful



# SETUP ACTION RUNNER
https://docs.github.com/en/actions/how-tos/manage-runners/self-hosted-runners/add-runners
https://docs.github.com/en/actions/how-tos/manage-runners/self-hosted-runners/configure-the-application
https://docs.github.com/en/actions/how-tos/deploy/configure-and-manage-deployments/control-deployments

# DockerHub Repo
https://hub.docker.com/repository/docker/rgarcia89/benefitlens/general

# Raspberry Pi 
https://wolfpaulus.com/rp5-cli/

## GitHub SSH Key
ssh-keygen -t ed25519 -C "$(whoami)@$(hostname) GitHub" -f ~/.ssh/id_ed25519
# press Enter for default location, optionally set a passphrase
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/id_ed25519
cat ~/.ssh/id_ed25519.pub
mkdir -p ~/.ssh
chmod 700 ~/.ssh
cat >> ~/.ssh/config <<'EOF'
Host github.com
  HostName github.com
  User git
  IdentityFile ~/.ssh/id_ed25519
  IdentitiesOnly yes
  AddKeysToAgent yes
EOF
chmod 700 ~/.ssh
chmod 600 ~/.ssh/id_ed25519 ~/.ssh/id_rsa 2>/dev/null || true
chmod 644 ~/.ssh/id_ed25519.pub ~/.ssh/id_rsa.pub 2>/dev/null || true


# IP Address Usage
The application tracks IP addresses in 4 main areas for different purposes:

1. Rate Limiting (Security)
Location: src/middleware.ts, src/lib/postgresql-rate-limit.ts
Purpose: Prevent abuse and ensure fair usage
Storage: rate_limits table
Data: IP address + request timestamps + counts
Retention: Automatically cleaned up after rate limit windows expire
2. Authentication Logging (Security & Audit)
Location: src/lib/auth-logger.ts
Purpose: Security monitoring and fraud detection
Storage: auth_logs table with ip_address inet column
Data: IP address + user agent + auth events (sign-in, failures, etc.)
Retention: Permanent for security audit trail
3. Search Analytics (Product Analytics)
Location: src/lib/analytics-tracker.ts
Purpose: Understanding user search behavior and improving search
Storage: search_queries table with ip_address inet column
Data: IP address + search terms + filters + results count
Retention: Permanent for analytics
4. Company Page Views (Product Analytics)
Location: src/lib/analytics-tracker.ts
Purpose: Track company profile engagement
Storage: company_page_views table with ip_address inet column
Data: IP address + company viewed + referrer + user agent
Retention: Permanent for analytics
5. Data Export Logging (Privacy Compliance)
Location: Database schema
Purpose: Track when users export their data (GDPR compliance)
Storage: data_export_logs table with ip_address inet column
Data: IP address + export type + user info
Retention: Permanent for compliance audit trail

-> 
📋 Next Steps (Optional)
For even better compliance, consider:

Setting up automated daily/weekly cleanup jobs
Adding geographic IP blocking for stricter jurisdictions
Implementing automated consent renewal reminders
Creating user-facing IP data deletion request forms
Create test cases that cover IP address tracking in all areas and verify data is correctly anonymized and cleaned up.


# identify problematic data in production db
-- Find all distinct event_type values in production
SELECT DISTINCT event_type FROM activity_log ORDER BY event_type;

-- Find rows that violate the constraint
SELECT event_type, COUNT(*) 
FROM activity_log 
WHERE event_type NOT IN (
  'benefit_added_to_company',
  'benefit_automatically_removed', 
  'benefit_disputed',
  'benefit_removal_dispute_approved',
  'benefit_removal_dispute_cancelled',
  'benefit_removal_dispute_rejected',
  'benefit_removal_dispute_submitted',
  'benefit_removed_from_company',
  'benefit_verified',
  'cache_refresh',
  'company_added',
  'session_cleanup',
  'user_deleted',
  'user_registered'
) 
GROUP BY event_type 
ORDER BY event_type;